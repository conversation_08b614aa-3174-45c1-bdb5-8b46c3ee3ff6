// 以下颜色是自定义颜色，没有限制，可以自行添加
// 如果有非常相近的颜色，可以和视觉同学确认下能不能统一
// 在添加颜色之前，先确认一下 token 里面有没有对应的颜色，比如 colorPrimaryBg 之类的
// token 对应的规范可以看 https://panda-design-team.github.io/
export const myColors = {
    aiBg: '#8c66ff1a',
    steelBlueLight: '#e9ebef',
    muted: '#f8fafc',
    textBlue: '#60a5fa',
    darkGray: '#4b5563',
    lightGray: '#9ca3af',
    whiteTransparent: 'rgba(255, 255, 255, 0.5)',
    blueGray: '#4b6c9f',
    lightBlueGray: '#4b6c9f26',
    primaryTransparent40: '#0080ff66',
    lightBlueTransparent15: '#8dc9fe26',
    lightBlueTransparent30: '#8dc9fe4d',
    legacyPrimary: '#0080ff',
    primaryFocus: '#cce5ff',
    primaryBg: '#e5f2ff',
    primaryBgTransparent: '#e5f2ff33',
    purple: '#8862e6',
    purpleTransparent: '#8862e626',
    success: '#00aa5b',
    successTransparent: '#00cc6d1a',
    yellow: '#f58300',
    // MCP评分颜色
    ratingYellow: '#f58300',
    ratingOrange: '#ff8c00',
    ratingRed: '#e62c4b',
    yellowTransparent: '#f583001a',
    infoTransparent15: '#317ff526',
    infoTransparent20: '#317ff533',
    // 从 icode 迁入
    aiGradientBg1: '#f5faff',
    aiGradientBg2: '#fff3e8',
    infoShadowColor: '#108cee26',
    // 一部分透明色
    blackTransparent60: 'rgba(0, 0, 0, 60%)',
    blackTransparent30: 'rgba(0, 0, 0, 30%)',
    blackTransparent15: 'rgba(0, 0, 0, 15%)',
    blackTransparent10: 'rgba(0, 0, 0, 10%)',
    whiteTransparent60: 'rgba(255, 255, 255, 60%)',
    redTransparent50: 'rgba(249, 213, 213, 50%)',
    blueTransparent60: 'rgba(191, 216, 246, 60%)',
    primaryLegacyTransparent5: '#108cee0d',
};
