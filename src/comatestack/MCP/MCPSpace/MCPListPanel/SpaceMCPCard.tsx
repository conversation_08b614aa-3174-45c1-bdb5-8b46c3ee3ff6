/* eslint-disable max-lines */
import {Flex, Divider, Typography, Tooltip} from 'antd';
import {memo, useCallback, MouseEvent, useMemo} from 'react';
import {useNavigate} from 'react-router-dom';
import {Button} from '@panda-design/components';
import cx from 'classnames';
import {MCPEditLink, MCPPlaygroundLink, MCPSpaceDetailLink} from '@/links/mcp';
import {MCPServerBase} from '@/types/mcp/mcp';
import {MCPSubscribeButton} from '@/components/MCP/MCPSubscribeButton';
import {useMCPWorkspaceId} from '@/components/MCP/hooks';
import MCPServerAvatar from '@/components/MCP/MCPServerAvatar';
import MCPCard from '@/design/MCP/MCPCard';
import {getServerTypeText} from '@/components/MCP/MCPServerTypeTag';
import TagGroup from '@/components/MCP/TagGroup';
import {MCPCollectButton} from '@/components/MCP/MCPCollectButton';
import PublishInfo from '@/components/MCP/PublishInfo';
import SvgEye from '@/icons/mcp/Eye';
import {
    actionButtonHoverStyle,
    cardContentStyle, containerCss,
    DescriptionContainer,
    DescriptionText,
    dividerStyle,
    EllipsisOverlay,
    formatCount,
    hoverActionsStyle,
    iconStyle,
    protocolTextStyle,
    statsContainerStyle,
} from '../../MCPSquare/SquirePanel/SquireMCPCard.styles';

interface Props {
    server: MCPServerBase;
    refresh: () => void;
}

const SpaceMCPCard = ({server, refresh}: Props) => {
    const spaceId = useMCPWorkspaceId();
    const navigate = useNavigate();

    const handleClick = useCallback(
        () => {
            navigate(MCPEditLink.toUrl({workspaceId: spaceId, mcpId: server.id, activeTab: 'tools'}));
        },
        [navigate, spaceId, server.id]
    );

    const tags = useMemo(
        () => (server.labels ?? []).map(label => label.labelValue),
        [server.labels]
    );

    const handleViewCountClick = useCallback(
        (e: MouseEvent) => {
            e.stopPropagation();
            navigate(
                MCPSpaceDetailLink.toUrl({mcpId: server.id, tab: 'overview'})
            );
        },
        [navigate, server.id]
    );
    const handlePlaygroundClick = useCallback(
        (e: MouseEvent) => {
            e.stopPropagation();
            window.open(
                MCPPlaygroundLink.toUrl({serverId: server.id}),
                '_blank'
            );
        },
        [server.id]
    );

    return (
        <MCPCard vertical onClick={handleClick} className={containerCss}>
            <Flex gap={14} align="center">
                <MCPServerAvatar icon={server.icon} />
                <Flex
                    vertical
                    justify="space-between"
                    style={cardContentStyle}
                    gap={4}
                >
                    <Typography.Title level={4} ellipsis>
                        {server.name}
                    </Typography.Title>
                    <Flex align="center" gap={12}>
                        <Typography.Text style={protocolTextStyle}>
                            {getServerTypeText(server.serverSourceType)}
                        </Typography.Text>
                        <Typography.Text style={protocolTextStyle}>
                            |
                        </Typography.Text>
                        <Typography.Text style={protocolTextStyle}>
                            {server.serverProtocolType}
                        </Typography.Text>
                    </Flex>
                </Flex>
            </Flex>
            <Tooltip title={server.description || '暂无描述'} placement="top">
                <DescriptionContainer>
                    <DescriptionText>
                        {server.description || '暂无描述'}
                    </DescriptionText>
                    <EllipsisOverlay />
                </DescriptionContainer>
            </Tooltip>
            <TagGroup
                labels={tags.map((label, index) => ({id: index, label}))}
                color="light-purple"
                prefix={null}
                style={{flexShrink: 1, overflow: 'hidden'}}
                gap={4}
            />
            <Divider style={dividerStyle} />
            <Flex justify="space-between" align="center">
                <Flex align="center" gap={12}>
                    <Tooltip title="浏览量">
                        <Flex
                            align="center"
                            gap={4}
                            onClick={handleViewCountClick}
                            className={statsContainerStyle}
                        >
                            <SvgEye style={iconStyle} />
                            {formatCount(server.viewCount)}
                        </Flex>
                    </Tooltip>
                </Flex>
                <PublishInfo
                    username={server.publishUser}
                    time={server.publishTime}
                />
            </Flex>
            <Flex
                align="center"
                justify="space-between"
                gap={10}
                className={`hover-actions ${hoverActionsStyle}`}
            >
                <MCPCollectButton
                    refresh={refresh}
                    favorite={server.favorite}
                    serverId={server.id}
                    className={cx(actionButtonHoverStyle)}
                    showText={false}
                    iconColor="#0083FF"
                />
                <MCPSubscribeButton
                    refresh={refresh}
                    workspaceId={spaceId}
                    id={server.id}
                    className={cx(actionButtonHoverStyle)}
                    showText={false}
                    iconColor="#0083FF"
                />
                <Button type="primary" onClick={handlePlaygroundClick}>
                    去MCP Playground使用
                </Button>
            </Flex>
        </MCPCard>
    );
};

export default memo(SpaceMCPCard);
